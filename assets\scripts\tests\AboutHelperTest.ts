/**
 * AboutHelper 测试文件
 * 用于验证 AboutHelper.ts 移植是否成功
 * <AUTHOR>
 */

import { _decorator, Component, Node, Vec3 } from 'cc';
import AboutHelper from '../helpers/AboutHelper';

const { ccclass, property } = _decorator;

@ccclass('AboutHelperTest')
export class AboutHelperTest extends Component {
    @property(Node)
    public testNode: Node | null = null;

    @property(Node)
    public targetNode: Node | null = null;

    start() {
        this.runTests();
    }

    private runTests(): void {
        console.log('开始 AboutHelper 测试...');

        // 测试 addChildCube 方法
        this.testAddChildCube();

        // 测试动画方法
        this.testAnimations();

        console.log('AboutHelper 测试完成！');
    }

    private testAddChildCube(): void {
        console.log('测试 addChildCube 方法...');
        
        if (this.testNode) {
            const childCube = AboutHelper.addChildCube(this.testNode, "1");
            if (childCube) {
                console.log('✓ addChildCube 测试通过');
            } else {
                console.log('✗ addChildCube 测试失败');
            }
        }
    }

    private testAnimations(): void {
        console.log('测试动画方法...');

        if (this.testNode && this.targetNode) {
            // 测试移动动画
            AboutHelper.action_moveCube_NoHaveCallBack(
                1.0, 
                this.testNode, 
                this.targetNode.position
            );
            console.log('✓ 移动动画测试启动');

            // 测试淡入动画
            AboutHelper.action_Three_NoCallBack(this.testNode);
            console.log('✓ 淡入动画测试启动');
        }
    }
}
